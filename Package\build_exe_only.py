"""
Script simplifié pour créer seulement l'exécutable avec PyInstaller
(Sans Inno Setup)
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# Configuration
APP_NAME = "Suivi_Generator"
APP_VERSION = "2.0"
MAIN_SCRIPT = "../Code_App.py"
ICON_PATH = "../Icone_App.png"
DIST_DIR = "dist"
BUILD_DIR = "build"

def clean_previous_builds():
    """Nettoie les builds précédents"""
    print("Nettoyage des builds precedents...")

    try:
        dirs_to_clean = [DIST_DIR, BUILD_DIR, "__pycache__"]
        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                try:
                    shutil.rmtree(dir_name)
                    print(f"   Supprime: {dir_name}")
                except Exception as e:
                    print(f"   Erreur suppression {dir_name}: {e}")

        # Supprimer les fichiers .spec
        try:
            for spec_file in Path(".").glob("*.spec"):
                spec_file.unlink()
                print(f"   Supprime: {spec_file}")
        except Exception as e:
            print(f"   Erreur suppression .spec: {e}")

        print("   Nettoyage termine")
        return True

    except Exception as e:
        print(f"   Erreur generale nettoyage: {e}")
        return True  # Continue même si nettoyage échoue

def install_dependencies():
    """Installe les dépendances nécessaires"""
    print("Installation des dependances...")

    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                      check=True, capture_output=True, text=True)
        print("   Dependances installees avec succes")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   Erreur lors de l'installation: {e}")
        print(f"   Details: {e.stderr if hasattr(e, 'stderr') else 'Aucun detail'}")
        return False
    except Exception as e:
        print(f"   Erreur inattendue: {e}")
        return False

def build_executable_simple():
    """Compile l'application avec PyInstaller (méthode simple)"""
    print("Compilation avec PyInstaller...")

    try:
        # Commande PyInstaller simplifiée
        cmd = [
            "pyinstaller",
            "--onefile",                    # Un seul fichier exécutable
            "--windowed",                   # Pas de console
            "--clean",                      # Nettoyer avant build
            "--noconfirm",                  # Pas de confirmation
            f"--name={APP_NAME}",          # Nom de l'exécutable
            f"--icon={ICON_PATH}",         # Icône
            "--add-data", f"{ICON_PATH};.", # Inclure l'icône dans l'exe
            "--add-data", "../Icone_App_Sharp.ico;.", # Inclure l'icône ultra-nette (priorité 1)
            "--add-data", "../Icone_App.ico;.", # Inclure l'icône .ico native Windows
            "--add-data", "../Icone_App_32x32.png;.", # Inclure l'icône optimisée 32x32
            "--add-data", "../Icone_App_16x16.png;.", # Inclure l'icône optimisée 16x16
            "--add-data", "../logo-2024.png;.", # Inclure le logo GUI dans l'exe
            "--hidden-import=pandas",       # Imports cachés
            "--hidden-import=openpyxl",
            "--hidden-import=xlrd",
            "--hidden-import=PIL",
            "--hidden-import=PIL.Image",
            "--hidden-import=PIL.ImageTk",
            "--hidden-import=tkinter",
            "--hidden-import=tkinter.filedialog",
            "--hidden-import=tkinter.messagebox",
            "--hidden-import=tkinter.ttk",
            MAIN_SCRIPT
        ]

        print("   Commande PyInstaller:")
        print(f"   {' '.join(cmd)}")
        print()

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print("   Compilation reussie!")
            return True
        else:
            print(f"   Erreur de compilation:")
            print(f"   STDOUT: {result.stdout}")
            print(f"   STDERR: {result.stderr}")
            return False

    except FileNotFoundError:
        print("   PyInstaller non trouve. Installez-le avec: pip install pyinstaller")
        return False

def test_executable():
    """Teste si l'exécutable fonctionne"""
    exe_path = Path(DIST_DIR) / f"{APP_NAME}.exe"

    if not exe_path.exists():
        print("   Executable non trouve")
        return False

    print(f"   Executable cree: {exe_path}")
    print(f"   Taille: {exe_path.stat().st_size / (1024*1024):.1f} MB")

    # Test rapide (lancement et fermeture immédiate)
    print("   Test de l'executable...")
    try:
        # Lancer l'exe et le fermer après 2 secondes
        import time

        process = subprocess.Popen([str(exe_path)])
        time.sleep(2)  # Laisser le temps de démarrer
        process.terminate()
        process.wait(timeout=5)

        print("   Test reussi - L'executable se lance correctement")
        return True

    except Exception as e:
        print(f"   Test non concluant: {e}")
        print("   Testez manuellement l'executable")
        return True  # On considère que c'est OK

def main():
    """Fonction principale de build"""
    print("BUILD EXECUTABLE SEULEMENT")
    print(f"Application: {APP_NAME} v{APP_VERSION}")
    print("=" * 50)

    # Étapes de build simplifiées
    steps = [
        ("Nettoyage", clean_previous_builds),
        ("Installation des dependances", install_dependencies),
        ("Compilation", build_executable_simple),
        ("Test", test_executable)
    ]

    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            print(f"Echec a l'etape: {step_name}")
            return False

    # Résumé final
    exe_path = Path(DIST_DIR) / f"{APP_NAME}.exe"
    if exe_path.exists():
        print("\n" + "=" * 50)
        print("BUILD REUSSI!")
        print(f"Executable: {exe_path}")
        print(f"Taille: {exe_path.stat().st_size / (1024*1024):.1f} MB")
        print("\nL'executable est pret a etre distribue!")
        print("Vous pouvez le copier sur d'autres PC sans installation")
        return True
    else:
        print("\nEchec: Executable non trouve")
        return False

if __name__ == "__main__":
    main()
